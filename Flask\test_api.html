<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test API</h1>
    <button onclick="testAPI()">Test /api/programs</button>
    <div id="result"></div>

    <script>
        function testAPI() {
            console.log('Testing /api/programs...');
            axios.get('/api/programs')
                .then(response => {
                    console.log('API Response:', response.data);
                    document.getElementById('result').innerHTML = 
                        '<pre>' + JSON.stringify(response.data, null, 2) + '</pre>';
                })
                .catch(error => {
                    console.error('API Error:', error);
                    document.getElementById('result').innerHTML = 
                        '<pre style="color: red;">Error: ' + error.message + '</pre>';
                });
        }

        // Auto test on load
        window.onload = testAPI;
    </script>
</body>
</html>
