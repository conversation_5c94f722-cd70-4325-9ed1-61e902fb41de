<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basket Arrangement</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('basket_arrangement.static', filename='css/basket_style.css') }}">
    <style>
        .basket-container {
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            border-radius: 0;
            box-sizing: border-box;
            overflow-y: auto;
        }

        .section {
            background: #2d2d2d;
            border: 1px solid #555;
            border-radius: 5px;
            margin: 15px 0;
            padding: 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
            border-bottom: 1px solid #555;
            padding-bottom: 5px;
        }

        .form-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
            gap: 10px;
        }

        .form-row label {
            min-width: 120px;
            color: #ddd;
        }

        .form-control {
            background: #333;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 3px;
            flex: 1;
        }

        .form-control:focus {
            border-color: #777;
            outline: none;
        }

        .btn {
            background: #444;
            border: 1px solid #666;
            color: #fff;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #555;
        }

        .btn-primary {
            background: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background: #0b5ed7;
        }

        .btn-success {
            background: #198754;
            border-color: #198754;
        }

        .btn-success:hover {
            background: #157347;
        }

        .btn-danger {
            background: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #bb2d3b;
        }

        .condition-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 8px 0;
            padding: 8px;
            background: #3a3a3a;
            border-radius: 4px;
        }

        .condition-row select,
        .condition-row input {
            background: #2d2d2d;
            border: 1px solid #555;
            color: #fff;
            padding: 6px;
            border-radius: 3px;
        }

        .id-input {
            flex: 1;
            min-width: 300px;
        }

        .time-selector {
            min-width: 150px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .remove-btn {
            background: #dc3545;
            border: none;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }

        .log-area {
            background: #1a1a1a;
            border: 1px solid #555;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            padding: 10px;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .hidden {
            display: none;
        }

        .id-count {
            color: #777;
            font-size: 12px;
            margin-left: 5px;
        }

        .tool-buttons {
            display: flex;
            gap: 2px;
        }

        .tool-btn {
            background: #414141;
            border: 1px solid #555;
            color: #ddd;
            width: 30px;
            height: 30px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tool-btn:hover {
            background: #505050;
        }

        .time-selection-popup {
            position: absolute;
            background: #2d2d2d;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            min-width: 200px;
        }

        .time-checkbox {
            display: block;
            margin: 5px 0;
            color: #fff;
        }

        .time-checkbox input {
            margin-right: 8px;
        }

        .popup-buttons {
            margin-top: 10px;
            text-align: center;
        }

        .popup-buttons button {
            margin: 0 5px;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: #2d2d2d;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 20px;
            min-width: 400px;
            max-width: 90%;
        }

        .modal-content h3 {
            color: #fff;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .basket-container {
                padding: 10px;
            }

            .condition-row {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .condition-row > * {
                width: 100%;
            }

            .tool-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="basket-container">
        <h1>🧺 Basket Arrangement</h1>
        <p>Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu</p>

        <!-- Google Sheet Information Section -->
        <div class="section">
            <div class="section-title">📊 Google Sheet Information</div>

            <div class="form-row">
                <label>Spreadsheet URL:</label>
                <input type="text" id="sheetUrl" class="form-control"
                       placeholder="Nhập Google Spreadsheet URL">
                <button id="loadSheetBtn" class="btn btn-primary">Load Sheet</button>
            </div>

            <div class="form-row">
                <label>Sheet:</label>
                <select id="sheetSelect" class="form-control" disabled>
                    <option value="">Chọn sheet...</option>
                </select>

                <label style="margin-left: 20px;">Cột ID đầu:</label>
                <input type="text" id="firstCol" class="form-control"
                       value="A" maxlength="3" style="width: 80px;">

                <label style="margin-left: 20px;">Số cột:</label>
                <input type="number" id="numColumns" class="form-control"
                       value="12" min="1" max="50" style="width: 80px;">

                <button id="mapColumnsBtn" class="btn" disabled>Map cột</button>
            </div>
        </div>

        <!-- Conditions Section -->
        <div class="section">
            <div class="section-title">⚙️ Điều kiện sắp xếp</div>

            <div id="conditionsContainer">
                <!-- Các dòng điều kiện sẽ được thêm vào đây -->
            </div>

            <div class="form-row">
                <button id="addConditionBtn" class="btn btn-success">+ Thêm điều kiện</button>
                <button id="processBtn" class="btn btn-primary" style="margin-left: auto;">
                    🚀 Xử lý sắp xếp
                </button>
            </div>
        </div>

        <!-- Log Section -->
        <div class="section">
            <div class="section-title">📝 Log xử lý</div>
            <div id="logArea" class="log-area"></div>
            <div class="form-row" style="margin-top: 10px;">
                <button id="clearLogBtn" class="btn">Xóa log</button>
            </div>
        </div>
    </div>

    <!-- Time Selection Popup Template -->
    <div id="timeSelectionPopup" class="time-selection-popup hidden">
        <div id="timeCheckboxes"></div>
        <div class="popup-buttons">
            <button class="btn" onclick="clearAllTimeSelection()">Bỏ chọn tất cả</button>
            <button class="btn btn-primary" onclick="closeTimePopup()">Đóng</button>
        </div>
    </div>

    <!-- Column Mapping Modal -->
    <div id="columnMappingModal" class="modal hidden">
        <div class="modal-content">
            <h3>Thiết lập ánh xạ cột Deal list</h3>
            <div class="form-row">
                <label>Shop ID:</label>
                <input type="text" id="mapShopId" class="form-control" value="A">
            </div>
            <div class="form-row">
                <label>Item ID:</label>
                <input type="text" id="mapItemId" class="form-control" value="B">
            </div>
            <div class="form-row">
                <label>GMV:</label>
                <input type="text" id="mapGmv" class="form-control" value="C">
            </div>
            <div class="form-row">
                <label>Review:</label>
                <input type="text" id="mapReview" class="form-control" value="D">
            </div>
            <div class="form-row">
                <label>NO:</label>
                <input type="text" id="mapNo" class="form-control" value="E">
            </div>
            <div class="form-row">
                <button class="btn btn-primary" onclick="saveColumnMapping()">Lưu</button>
                <button class="btn" onclick="closeColumnMapping()">Hủy</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/basket_arrangement.js') }}"></script>
</body>
</html>
