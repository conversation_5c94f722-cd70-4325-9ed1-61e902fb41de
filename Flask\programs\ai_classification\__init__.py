from flask import Blueprint, render_template, request, jsonify, session
import traceback

# Thông tin chương trình
NAME = "AI Classification"
DESCRIPTION = "Phân loại sản phẩm thông minh bằng AI với OpenAI GPT"
ICON = "🤖"

# Tạo Blueprint
ai_classification_bp = Blueprint('ai_classification', __name__,
                                template_folder='templates',
                                static_folder='static',
                                url_prefix='/ai_classification')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(ai_classification_bp)

@ai_classification_bp.route('/')
def index():
    """Trang chính của AI Classification"""
    return render_template('programs/ai_classification.html')

@ai_classification_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '')

        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'data': {
                'sheet_id': 'test_sheet_id',
                'sheet_names': ['Brand', 'Deal list'],
                'total_sheets': 2
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@ai_classification_bp.route('/api/get_classifications', methods=['POST'])
def get_classifications():
    """API để lấy danh sách phân loại từ database"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'classifications': [
                {'brand_code': 'TEST001', 'classification': 'serum', 'product_count': 5},
                {'brand_code': 'TEST002', 'classification': 'son môi', 'product_count': 3}
            ]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/process_classification', methods=['POST'])
def process_classification():
    """API để xử lý phân loại AI"""
    try:
        data = request.get_json()
        model = data.get('model', 'gpt-4o-mini')
        last_row = data.get('last_row', 100)
        
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'result': {
                'message': 'Đã xử lý phân loại thành công (test mode)',
                'processed_count': 25,
                'updated_count': 20,
                'api_calls': 15,
                'tokens_used': 1500,
                'classifications': [
                    {'brand_code': 'TEST001', 'classification': 'serum', 'products': ['Product A', 'Product B']},
                    {'brand_code': 'TEST002', 'classification': 'son môi', 'products': ['Product C']}
                ]
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@ai_classification_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'mapping': {
                'brand_code': 'A',
                'brand_type': 'B',
                'brand_name': 'C',
                'deal_brand_code': 'A',
                'deal_product': 'B',
                'deal_price': 'C'
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        mapping = data.get('mapping', {})

        # Lưu mapping vào session
        if 'ai_classification' not in session:
            session['ai_classification'] = {}
        session['ai_classification']['column_mapping'] = mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@ai_classification_bp.route('/api/get_database_stats', methods=['GET'])
def get_database_stats():
    """API để lấy thống kê database"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'stats': {
                'total_products': 150,
                'total_brands': 25,
                'total_classifications': 12,
                'price_history_entries': 300
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
