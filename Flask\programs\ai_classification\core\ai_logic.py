"""
AI Classification Logic - Logic chính cho phân loại sản phẩm bằng AI
Copy và clean code từ ai_classification.py gốc
"""

import re
import time
import json
import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from difflib import SequenceMatcher
from openai import OpenAI

# Cấu hình logger
logger = logging.getLogger(__name__)

# Cấu hình OpenAI
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
API_CALL_DELAY = 0.5

# Biến toàn cục để theo dõi API usage
api_call_count = 0
total_tokens_used = 0

# Prompt template cho phân loại
PROMPT_TEMPLATE = (
    "Bạn là một chuyên gia phân loại và trích xuất thông tin sản phẩm với khả năng hiểu tiếng Việt chuyên sâu. "
    "Từ thông tin sản phẩm dưới đây, hãy phân tích và trả về các thông tin sau: \n\n"
    "{price_info}"
    "Tên sản phẩm: {product_description}\n\n"
    "Luật trích xuất chi tiết:\n"
    "1. **type_of_product**: loại sản phẩm chính (VD: 'nồi chiên không dầu', 'sữa rửa mặt', 'kem chống nắng'...).\n"
    "   Quy tắc đặc biệt cho type_of_product:\n"
    "   • CHỈ và CHỈ KHI là nước uống/thức uống collagen → 'nước uống collagen'\n"
    "   • camera/camera wifi/camera ip → 'camera'\n"
    "   • tinh chất/serum/essence → 'serum'\n"
    "   • tẩy da chết/scrub/exfoliator → 'tẩy da chết'\n"
    "   • điện thoại/smartphone → 'điện thoại'\n"
    "   • tã/bỉm → 'tã'\n"
    "   • kit trắng răng/trắng răng → 'kit trắng răng'\n"
    "   • túi xách/túi đeo vai → 'túi xách'\n"
    "   • máy lọc không khí/lọc không khí → 'máy lọc không khí'\n"
    "   • CHỈ và CHỈ KHI chứa chính xác từ khóa: áo ngực/quần lót nữ/bra/áo lót → 'đồ lót nữ'\n"
    "   • son/lipstick/son tint/balm/kem môi → 'son môi'\n"
    "   • phấn mắt/mascara/eyeliner/kẻ mắt/má hồng/eyeshadow/phấn → 'makeup'\n"
    "   • mặt nạ/mask/mặt nạ đất sét/mặt nạ giấy/mặt nạ ngủ → 'mặt nạ dưỡng da'\n"
    "   • nước cân bằng/toner/nước hoa hồng → 'toner'\n"
    "2. **brand**: tên thương hiệu nếu có (VD: 'LocknLock', 'Anessa'); nếu không có => chuỗi rỗng.\n"
    "3. **product_name**: phần tên riêng của sản phẩm. (VD 'Máy ép chậm Sunhouse SHD5515', 'Máy lọc không khí Sunhouse SHD-15AP9715')\n"
    "4. **code**: mã sản phẩm (VD 'F9', 'EH015', 'MC210K', 'SHD-15AP9715', ...). Nếu không tìm thấy => chuỗi rỗng.\n"
    "5. **capacity**: dung tích, size, công suất, v.v. (VD '50ml', '250g', 'size 41', '1000W', '10000mAh').\n"
    "6. **quantity**: số lượng sản phẩm trong combo/bộ (mặc định là 1).\n\n"
    "Hãy trả lời theo định dạng sau (KHÔNG trả về dạng JSON):\n"
    "Phân loại: [type_of_product]\n"
    "Brand: [brand]\n"
    "Tên sản phẩm: [product_name]\n"
    "Mã sản phẩm: [code]\n"
    "Dung tích/Kích thước: [capacity]\n"
    "Số lượng: [quantity]"
)

def get_data_directory():
    """Tạo và trả về đường dẫn đến thư mục lưu trữ dữ liệu cho AI Classification"""
    import os
    try:
        app_data_path = os.environ.get('LOCALAPPDATA', '')
        if not app_data_path:
            app_data_path = str(Path.home() / "AppData" / "Local")
            if not os.path.exists(app_data_path):
                documents_path = os.path.join(str(Path.home()), "Documents")
                app_data_path = os.path.join(documents_path, "Data All in One Data")
                os.makedirs(app_data_path, exist_ok=True)

        data_dir = Path(app_data_path) / "Data All in One" / "AI Classification"
        os.makedirs(data_dir, exist_ok=True)

        # Kiểm tra quyền ghi
        test_file = data_dir / "test_write_permission.txt"
        try:
            with open(test_file, 'w') as f:
                f.write("Test write permission")
            os.remove(test_file)
        except Exception as e:
            documents_path = os.path.join(str(Path.home()), "Documents")
            alt_data_dir = Path(documents_path) / "Data All in One" / "AI Classification"
            os.makedirs(alt_data_dir, exist_ok=True)
            return alt_data_dir

        return data_dir
    except Exception as e:
        documents_path = os.path.join(str(Path.home()), "Documents")
        fallback_dir = Path(documents_path) / "Data All in One" / "AI Classification"
        os.makedirs(fallback_dir, exist_ok=True)
        return fallback_dir

# Khởi tạo data directory
DATA_DIR = get_data_directory()
DB_PATH = DATA_DIR / "active_learning.db"

def setup_database():
    """Initialize SQLite database for active learning storage"""
    db_path = DATA_DIR / "active_learning.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # Create feedback table with product names
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS feedback (
        brand_code TEXT,
        classification TEXT,
        product_names TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (brand_code)
    )
    ''')

    # Create product classification table for individual product mappings with price
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_classifications (
        product_name TEXT,
        classification TEXT,
        brand_code TEXT,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Create similar product classification table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS similar_products (
        product_name TEXT,
        similar_to TEXT,
        classification TEXT,
        similarity_score REAL,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Tạo bảng lưu trữ brands (thương hiệu)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS brands (
        brand_code TEXT PRIMARY KEY,
        brand_name TEXT,
        classification TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ thiết lập
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ lịch sử giá với thông tin nguồn và ngày
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS price_history (
        product_name TEXT,
        price INTEGER,
        live_session TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name, timestamp)
    )
    ''')

    # Tạo bảng sync_status để theo dõi trạng thái đồng bộ lên Google Sheets
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sync_status (
        brand_code TEXT PRIMARY KEY,
        sync_status INTEGER DEFAULT 0,
        last_sync_time TIMESTAMP,
        last_classification TEXT,
        spreadsheet_id TEXT
    )
    ''')

    conn.commit()
    return conn

def post_process(result: str) -> str:
    """Parse the result string to extract proper category name"""
    text = result.strip().lower()

    # Dictionary for common phrases to normalize
    if ("nước uống collagen" in text or
        "thức uống collagen" in text or
        "collagen uống" in text or
        ("nước uống" in text and "collagen" in text) or
        ("thức uống" in text and "collagen" in text)):
        return "nước uống collagen"

    if any(term in text for term in ["camera", "camera wifi", "camera ip"]):
        return "camera"

    if any(term in text for term in ["tinh chất", "serum", "essence"]):
        return "serum"

    if any(term in text for term in ["tẩy da chết", "scrub", "exfoliator"]):
        return "tẩy da chết"

    if any(term in text for term in ["điện thoại", "smartphone"]):
        return "điện thoại"

    if any(term in text for term in ["tã", "bỉm"]):
        return "tã"

    if any(term in text for term in ["kit trắng răng", "trắng răng"]):
        return "kit trắng răng"

    if any(term in text for term in ["túi xách", "túi đeo vai"]):
        return "túi xách"

    if any(term in text for term in ["máy lọc không khí", "lọc không khí"]):
        return "máy lọc không khí"

    # Kiểm tra CHỈ và CHỈ KHI chứa chính xác từ khóa cho "Đồ lót nữ"
    exact_keywords = ["áo ngực", "quần lót nữ", "bra", "áo lót"]
    for keyword in exact_keywords:
        if keyword in text:
            return "đồ lót nữ"

    if any(term in text for term in ["son", "lipstick", "son tint", "balm", "kem môi"]):
        return "son môi"

    if any(term in text for term in ["phấn mắt", "mascara", "eyeliner", "kẻ mắt", "má hồng", "eyeshadow", "phấn"]):
        return "makeup"

    if any(term in text for term in ["mặt nạ", "mask", "mặt nạ đất sét", "mặt nạ giấy", "mặt nạ ngủ"]):
        return "mặt nạ dưỡng da"

    if any(term in text for term in ["nước cân bằng", "toner", "nước hoa hồng"]):
        return "toner"

    # Get first line if multiple lines
    if '\n' in result:
        text = result.split('\n')[0].strip()

    return text.strip().lower()

def string_similarity(str1, str2):
    """Calculate similarity between two strings"""
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

def format_price_vnd(price):
    """Format price in VND"""
    if price == 0:
        return "0"
    return f"{price:,}".replace(",", ".")

# Initialize database if not exists
if not DB_PATH.exists():
    setup_database()

def find_similar_product(product_name, threshold=0.85):
    """
    Finds the most similar product in the database
    Returns tuple (product_name, similarity_score) or None if no match
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Kiểm tra bảng similar_products có tồn tại không
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
        if not cursor.fetchone():
            # Tạo bảng nếu chưa tồn tại
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS similar_products (
                product_name TEXT,
                similar_to TEXT,
                classification TEXT,
                similarity_score REAL,
                price INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_name)
            )
            ''')
            conn.commit()
            conn.close()
            return None

        # Lấy tất cả sản phẩm từ database
        cursor.execute("SELECT product_name FROM product_classifications")
        stored_products = [row[0] for row in cursor.fetchall()]

        if not stored_products:
            conn.close()
            return None

        # Tìm sản phẩm giống nhất
        best_similarity = 0
        best_match = None

        for stored_product in stored_products:
            similarity = string_similarity(product_name.lower(), stored_product.lower())
            if similarity > best_similarity and similarity >= threshold:
                best_similarity = similarity
                best_match = stored_product

        conn.close()

        if best_match:
            return (best_match, best_similarity)
        return None
    except Exception as e:
        print(f"Error finding similar product: {e}")
        try:
            conn.close()
        except:
            pass
        return None

def analyze_combo_set(product_name, model="gpt-4o-mini"):
    """
    Phân tích tên sản phẩm combo/bộ để xác định số lượng sản phẩm trong bộ
    Trả về số lượng sản phẩm dự đoán
    """
    global api_call_count, total_tokens_used

    product_lower = product_name.lower()

    # Detect patterns with regex
    number_pattern = r"(?:combo|set|bộ)(?:\s+x|\s+of\s+|\s+|-)(\d+)"
    pieces_pattern = r"(\d+)(?:\s+|-)(?:món|piece|miếng|items|sp|sản phẩm)"
    in_pattern = r"(\d+)(?:\s*|-)in(?:\s*|-)1"

    # Check for common patterns first
    for pattern in [number_pattern, pieces_pattern, in_pattern]:
        matches = re.findall(pattern, product_lower)
        if matches:
            try:
                return int(matches[0])
            except:
                pass

    # Check for "bộ đôi" = 2
    if any(term in product_lower for term in ["bộ đôi", "combo đôi", "set đôi"]):
        return 2

    # Check for "bộ 3" without space
    if any(term in product_lower for term in ["bộ3", "combo3", "set3"]):
        return 3

    # If none of the patterns matched, but it contains combo/set/bộ keywords, use API
    if any(term in product_lower for term in ["combo", "set", "bộ"]) and model and OPENAI_API_KEY:
        prompt = (
            f"Phân tích tên sản phẩm sau và xác định có BAO NHIÊU SẢN PHẨM trong bộ: \"{product_name}\"\n"
            f"Chỉ trả về số lượng dưới dạng số nguyên (ví dụ: 2, 3, 4...).\n"
            f"Nếu không xác định được, trả về 1.\n"
        )

        try:
            openai_client = OpenAI(api_key=OPENAI_API_KEY)
            response = openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=10
            )
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception:
                pass

            result = response.choices[0].message.content.strip()
            api_call_count += 1
            time.sleep(API_CALL_DELAY)

            # Lấy số từ kết quả
            try:
                count = int(re.search(r"\d+", result).group())
                if 1 <= count <= 10:  # Giới hạn hợp lý
                    return count
            except:
                pass
        except:
            pass

    # Fallback: có combo nhưng không xác định được số lượng
    if any(term in product_lower for term in ["combo", "set", "bộ", "kit"]):
        return 2
    return 1

def extract_product_category(product_info, price=0, prompt_template=PROMPT_TEMPLATE, model="gpt-4o-mini"):
    """
    Phân loại sản phẩm bằng cách gọi API OpenAI để phân tích.

    Returns: tuple (classification_result, prompt, extracted_data)
    - classification_result: kết quả phân loại cuối cùng
    - prompt: nội dung prompt đã sử dụng
    - extracted_data: dữ liệu trích xuất thêm từ sản phẩm (nếu có)
    """
    global api_call_count, total_tokens_used

    # Kiểm tra cơ sở dữ liệu nếu sản phẩm đã được phân loại trước đó
    similar_product = find_similar_product(product_info)
    if similar_product:
        print(f"Using similar product match: '{product_info}' -> '{similar_product[0]}' (Score: {similar_product[1]:.2f})")
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra bảng product_classifications có tồn tại không
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
            if not cursor.fetchone():
                # Tự động tạo bảng nếu chưa tồn tại
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_classifications (
                    product_name TEXT,
                    classification TEXT,
                    brand_code TEXT,
                    price INTEGER DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (product_name)
                )
                ''')
                conn.commit()
                conn.close()
                return None, "", None

            # Thực hiện truy vấn
            cursor.execute("SELECT classification, brand_code FROM product_classifications WHERE product_name = ?",
                        (similar_product[0],))
            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0], "", None
        except Exception as e:
            print(f"Error querying database: {e}")
            try:
                conn.close()
            except:
                pass

    # Phân tích số lượng sản phẩm nếu là combo/set
    combo_quantity = 1
    if any(term in product_info.lower() for term in ['bộ', 'combo', 'set']):
        combo_quantity = analyze_combo_set(product_info, model)

    # Xử lý prompt
    product_description = product_info
    price_text = ""
    if price > 0:
        price_text = f"Giá sản phẩm: {format_price_vnd(price)} VND\n"

    # Chuẩn bị prompt
    prompt = prompt_template.format(product_description=product_description, price_info=price_text)

    # Số lần thử lại khi gặp lỗi API
    max_retries = 3
    retry_delay = 2
    retry_count = 0

    if not OPENAI_API_KEY:
        # Nếu không có API key, trả về phân loại mặc định
        return "unknown", prompt, {"error": "No OpenAI API key", "product_info": product_info, "quantity": combo_quantity}

    while retry_count < max_retries:
        try:
            # Gọi API OpenAI
            openai_client = OpenAI(api_key=OPENAI_API_KEY)
            response = openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "Bạn là một trợ lý AI chuyên phân loại sản phẩm trong lĩnh vực thời trang, mỹ phẩm và hàng tiêu dùng."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            # Tính token đã sử dụng
            api_call_count += 1
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception as e:
                print(f"Lỗi tính token: {str(e)}")

            # Lấy kết quả
            result_text = response.choices[0].message.content.strip()

            # Xử lý kết quả từ AI
            result_lines = result_text.strip().split('\n')
            classification = None
            extracted_data = {}

            for line in result_lines:
                line = line.strip()
                if not line:
                    continue

                # Tìm dòng bắt đầu bằng "Phân loại:" hoặc "Classification:"
                if line.lower().startswith(("phân loại:", "classification:")):
                    classification = line.split(":", 1)[1].strip()
                    extracted_data["type_of_product"] = classification
                elif line.lower().startswith("brand:"):
                    extracted_data["brand"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("tên sản phẩm:"):
                    extracted_data["product_name"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("mã sản phẩm:"):
                    extracted_data["code"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith(("dung tích", "kích thước")):
                    extracted_data["capacity"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("số lượng:"):
                    try:
                        quantity_str = line.split(":", 1)[1].strip()
                        quantity = int(re.search(r'\d+', quantity_str).group())
                        extracted_data["quantity"] = quantity
                    except:
                        extracted_data["quantity"] = 1

            # Nếu không thể trích xuất phân loại theo định dạng, sử dụng toàn bộ kết quả
            if not classification:
                classification = post_process(result_text)
            else:
                classification = post_process(classification)

            # Thêm thông tin còn thiếu
            extracted_data["product_info"] = product_info
            if "quantity" not in extracted_data:
                extracted_data["quantity"] = combo_quantity

            # Lưu vào DB cho các lần tìm kiếm tương đồng sau này
            if similar_product is None:
                try:
                    conn = sqlite3.connect(str(DB_PATH))
                    cursor = conn.cursor()

                    # Kiểm tra và tạo bảng similar_products nếu chưa tồn tại
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
                    if not cursor.fetchone():
                        cursor.execute('''
                        CREATE TABLE IF NOT EXISTS similar_products (
                            product_name TEXT,
                            similar_to TEXT,
                            classification TEXT,
                            similarity_score REAL,
                            price INTEGER DEFAULT 0,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (product_name)
                        )
                        ''')

                    cursor.execute(
                        "INSERT OR REPLACE INTO similar_products (product_name, similar_to, classification, similarity_score, price) VALUES (?, ?, ?, ?, ?)",
                        (product_info, product_info, classification, 1.0, price)
                    )
                    conn.commit()
                    conn.close()
                except Exception as e:
                    print(f"Error saving to similar_products: {e}")
                    try:
                        conn.close()
                    except:
                        pass

            return classification, prompt, extracted_data

        except Exception as e:
            retry_count += 1
            error_msg = str(e)

            print(f"Error processing product: {product_info}")
            print(f"Error: {error_msg}")

            # Kiểm tra lỗi Cloudflare hoặc giới hạn tần suất
            is_rate_limit = "rate limit" in error_msg.lower() or "rate_limit" in error_msg.lower()
            is_cloudflare = "520" in error_msg or "cloudflare" in error_msg.lower()

            if retry_count < max_retries:
                wait_time = retry_delay * (2 ** (retry_count - 1))
                if is_rate_limit:
                    wait_time *= 2

                print(f"Retrying in {wait_time} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(wait_time)
            else:
                if is_rate_limit:
                    if model != "gpt-3.5-turbo":
                        print("Rate limit exceeded, trying fallback model...")
                        return extract_product_category(product_info, price, prompt_template, "gpt-3.5-turbo")
                elif is_cloudflare:
                    print("Connection error from Cloudflare. Network issue or OpenAI service unavailable.")
                    return "Unknown", prompt, {"error": "Cloudflare connection error", "product_info": product_info}

                fallback_classification = "Unknown"
                return fallback_classification, prompt, {"error": error_msg, "product_info": product_info}
